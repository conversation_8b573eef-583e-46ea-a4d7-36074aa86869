import {
  ArrowLeftOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  TeamOutlined,
  DesktopOutlined,
  CalendarOutlined,
  FileTextOutlined,
  ExclamationCircleOutlined,
  SafetyOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Row,
  Typography,
  Space,
  Alert,
  Modal,
  Input,
  Spin,
  Form,
} from 'antd';
import dayjs from 'dayjs';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { OrderStatusTag, OrderTypeTag, TextCompression } from '@/components';
import CurrencyTag from '@/components/Tags/CurrencyTag.tsx';
import { ROUTES } from '@/constants/routes';
import { useOrderNotifications } from '@/hooks';
import { useOrder, useAdminReviewDecision } from '@/hooks/useOrders';
import { useResponsive } from '@/hooks/useResponsive';
import { useUserStore } from '@/stores';
import type { Order } from '@/types';
import { formatOrderAmount } from '@/utils/currencyUtils';

const { Title, Text, Paragraph } = Typography;
const { TextArea, Password } = Input;

const OrderReview: React.FC = () => {
  const { orderId } = useParams<{ orderId: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation('orderReview');
  const { isMobile } = useResponsive();
  const { isDark } = useUserStore();

  // Local state
  const [approveModalOpen, setApproveModalOpen] = useState(false);
  const [cancelModalOpen, setCancelModalOpen] = useState(false);
  const [approveForm] = Form.useForm();
  const [cancelForm] = Form.useForm();

  // Fetch order data
  const { data: orderResponse, isLoading, error, refetch } = useOrder(orderId!);
  const order = orderResponse?.data;

  // Mutations
  const adminReviewMutation = useAdminReviewDecision();

  // Setup order notifications to automatically refetch data
  useOrderNotifications({
    onOrderUpdate: refetch,
    enabled: true,
  });

  const handleBack = () => {
    navigate(ROUTES.ORDERS);
  };

  const handleApproveOrder = async () => {
    if (!order) return;

    try {
      const values = await approveForm.validateFields();

      await adminReviewMutation.mutateAsync({
        orderId: order.orderId,
        reviewData: {
          orderStatus: 'APPROVED',
          reason: values.notes,
          adminPassword: values.password,
        },
      });

      setApproveModalOpen(false);
      approveForm.resetFields();
      // Navigate back after successful approval
      handleBack();
    } catch (error) {
      console.log(error);
      // Error is handled by the mutation or form validation
    }
  };

  const handleCancelOrder = async () => {
    if (!order) return;

    try {
      const values = await cancelForm.validateFields();

      await adminReviewMutation.mutateAsync({
        orderId: order.orderId,
        reviewData: {
          orderStatus: 'CANCELLED',
          reason: values.reason,
          adminPassword: values.password,
        },
      });

      setCancelModalOpen(false);
      cancelForm.resetFields();
      // Navigate back after successful cancellation
      handleBack();
    } catch (error) {
      console.log(error);
      // Error is handled by the mutation or form validation
    }
  };

  const canReviewOrder = (order: Order) => {
    // Only allow review for completed orders
    return order.orderStatus === 'COMPLETED';
  };

  if (isLoading) {
    return (
      <div className='flex items-center justify-center min-h-[400px]'>
        <Spin size='large' />
      </div>
    );
  }

  if (error || !order || !orderId) {
    return (
      <div className='flex flex-col items-center justify-center min-h-[400px] space-y-4'>
        <ExclamationCircleOutlined className='text-6xl text-red-500' />
        <Title level={3}>{t('orderNotFound')}</Title>
        <Text type='secondary'>{t('orderNotFoundDescription')}</Text>
        <Button type='primary' onClick={handleBack}>
          {t('backToOrders')}
        </Button>
      </div>
    );
  }

  const isReviewable = canReviewOrder(order);

  return (
    <div className={`h-screen flex flex-col ${isDark ? 'bg-gray-900' : 'bg-gray-50'}`}>
      {/* Header - Fixed at top */}
      <div className={`${isMobile ? 'p-4' : 'p-6'} border-b ${isDark ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-white'} shadow-sm`}>
        <div className='max-w-7xl mx-auto'>
          <div className='flex items-center justify-between mb-4'>
            {/* Left side - Back button */}
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={handleBack}
              className='flex items-center'
              size='large'
            >
              {!isMobile && t('backToOrders')}
            </Button>

            {/* Right side - Action buttons */}
            {isReviewable && (
              <Space size='middle'>
                <Button
                  type='primary'
                  danger
                  icon={<CloseCircleOutlined />}
                  onClick={() => setCancelModalOpen(true)}
                  size='large'
                  className='shadow-lg hover:shadow-xl transition-all duration-300'
                >
                  {!isMobile && t('cancelOrder')}
                </Button>
                <Button
                  type='primary'
                  icon={<CheckCircleOutlined />}
                  onClick={() => setApproveModalOpen(true)}
                  size='large'
                  className='bg-green-600 hover:bg-green-700 border-green-600 hover:border-green-700 shadow-lg hover:shadow-xl transition-all duration-300'
                >
                  {!isMobile && t('approveOrder')}
                </Button>
              </Space>
            )}
          </div>

          {/* Title section - aligned with card */}
          <div>
            <Title
              level={2}
              className={`!mb-1 ${isDark ? 'text-white' : 'text-gray-900'}`}
            >
              {t('title')}
            </Title>
            <div className='flex items-center space-x-4'>
              <Text
                strong
                className={`text-lg ${isDark ? 'text-white' : 'text-gray-900'}`}
              >
                {order?.uxmOrderId}
              </Text>
              <OrderStatusTag status={order?.orderStatus || ''} />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content - Flexible height */}
      <div className='flex-1 overflow-auto'>
        <div className={`${isMobile ? 'p-4' : 'p-6'} mx-auto h-full flex justify-center`}>
          {/* Status Alert */}
          {!isReviewable && (
            <Alert
              message={t('orderReviewNotAvailable')}
              description={t('orderReviewNotAvailableDescription')}
              type='warning'
              showIcon
              className='mb-6 shadow-sm rounded-lg max-w-4xl w-full'
            />
          )}

          {/* Main Content Card - Narrower and Taller */}
          <Card
            className={`shadow-lg rounded-2xl border ${isDark ? 'bg-gray-800 border-gray-600' : 'bg-white border-gray-200'} max-w-4xl w-full`}
            bodyStyle={{ padding: '32px' }}
          >
            {/* Transaction Summary - Most Important */}
            <div className='mb-8'>
              <div className='flex items-center mb-4'>
                <div className={`${isDark ? 'bg-blue-600' : 'bg-blue-500'} p-2 rounded-lg mr-3`}>
                  <ShoppingCartOutlined className='text-white text-lg' />
                </div>
                <Title level={4} className={`!mb-0 ${isDark ? 'text-white' : 'text-gray-900'}`}>
                  Transaction Summary
                </Title>
              </div>

              <div className={`${isDark ? 'bg-gray-700 border-gray-600' : 'bg-green-50 border-green-200'} p-6 rounded-xl border-2`}>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                  <div className='text-center md:text-left'>
                    <Text type='secondary' className='text-sm font-medium block mb-2'>
                      {t('totalAmount')}
                    </Text>
                    <Text strong className={`text-3xl font-bold ${isDark ? 'text-green-400' : 'text-green-600'}`}>
                      {formatOrderAmount(order.quantity * order.pricePerUnit, order.currencyUnit)}
                    </Text>
                  </div>
                  <div className='grid grid-cols-2 gap-4'>
                    <div>
                      <Text type='secondary' className='text-sm font-medium block mb-2'>
                        {t('quantity')}
                      </Text>
                      <Text strong className='text-xl'>
                        {order.quantity.toLocaleString()}
                      </Text>
                    </div>
                    <div>
                      <Text type='secondary' className='text-sm font-medium block mb-2'>
                        {t('pricePerUnit')}
                      </Text>
                      <Text strong className='text-xl'>
                        {formatOrderAmount(order.pricePerUnit, order.currencyUnit)}
                      </Text>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Order Details */}
            <div className='mb-8'>
              <div className='flex items-center mb-4'>
                <div className={`${isDark ? 'bg-purple-600' : 'bg-purple-500'} p-2 rounded-lg mr-3`}>
                  <FileTextOutlined className='text-white text-lg' />
                </div>
                <Title level={4} className={`!mb-0 ${isDark ? 'text-white' : 'text-gray-900'}`}>
                  Order Details
                </Title>
              </div>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className={`${isDark ? 'bg-gray-700' : 'bg-gray-50'} p-4 rounded-lg`}>
                  <Text type='secondary' className='text-sm font-medium block mb-2'>
                    {t('orderType')}
                  </Text>
                  <OrderTypeTag type={order.orderType} />
                </div>

                <div className={`${isDark ? 'bg-gray-700' : 'bg-gray-50'} p-4 rounded-lg`}>
                  <Text type='secondary' className='text-sm font-medium block mb-2'>
                    {t('status')}
                  </Text>
                  <OrderStatusTag status={order.orderStatus} />
                </div>

                <div className={`${isDark ? 'bg-gray-700' : 'bg-gray-50'} p-4 rounded-lg`}>
                  <Text type='secondary' className='text-sm font-medium block mb-2'>
                    {t('currency')}
                  </Text>
                  <CurrencyTag currency={order.currencyUnit} />
                </div>

                <div className={`${isDark ? 'bg-gray-700' : 'bg-gray-50'} p-4 rounded-lg`}>
                  <Text type='secondary' className='text-sm font-medium block mb-2'>
                    {t('uxmOrderId')}
                  </Text>
                  <TextCompression
                    text={order.uxmOrderId}
                    maxLength={12}
                    size='default'
                    showCopy={!isMobile}
                  />
                </div>
              </div>
            </div>

            {/* ID Information Section */}
            <div className='grid grid-cols-2 md:grid-cols-4 gap-3 mb-6'>
              <div
                className={`${isDark ? 'bg-gray-700' : 'bg-gray-50'} p-3 rounded-lg`}
              >
                <Text
                  type='secondary'
                  className='text-xs font-medium mb-1 block'
                >
                  {t('orderId')}
                </Text>
                <TextCompression
                  text={order.orderId}
                  maxLength={isMobile ? 8 : 12}
                  size='small'
                  showCopy={!isMobile}
                />
              </div>

              <div
                className={`${isDark ? 'bg-gray-700' : 'bg-gray-50'} p-3 rounded-lg`}
              >
                <Text
                  type='secondary'
                  className='text-xs font-medium mb-1 block'
                >
                  {t('userId')}
                </Text>
                <TextCompression
                  text={order.userId}
                  maxLength={isMobile ? 8 : 12}
                  size='small'
                  showCopy={!isMobile}
                />
              </div>

              <div
                className={`${isDark ? 'bg-gray-700' : 'bg-gray-50'} p-3 rounded-lg`}
              >
                <Text
                  type='secondary'
                  className='text-xs font-medium mb-1 block'
                >
                  {t('staffId')}
                </Text>
                <TextCompression
                  text={order.staffId}
                  maxLength={isMobile ? 8 : 12}
                  size='small'
                  showCopy={!isMobile}
                />
              </div>

              <div
                className={`${isDark ? 'bg-gray-700' : 'bg-gray-50'} p-3 rounded-lg`}
              >
                <Text
                  type='secondary'
                  className='text-xs font-medium mb-1 block'
                >
                  {t('counterId')}
                </Text>
                <TextCompression
                  text={order.counterId}
                  maxLength={isMobile ? 8 : 12}
                  size='small'
                  showCopy={!isMobile}
                />
              </div>
            </div>

            {/* Timestamps - Compact Row */}
            <div className='grid grid-cols-1 md:grid-cols-2 gap-3 mb-6'>
              <div
                className={`${isDark ? 'bg-gray-700' : 'bg-gray-50'} p-3 rounded-lg`}
              >
                <Text
                  type='secondary'
                  className='text-xs font-medium mb-1 block'
                >
                  {t('createdAt')}
                </Text>
                <Space size='small'>
                  <CalendarOutlined
                    className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}
                  />
                  <Text className='text-sm'>
                    {dayjs(order.createdAt).format('MMM DD, YYYY HH:mm')}
                  </Text>
                </Space>
              </div>

              <div
                className={`${isDark ? 'bg-gray-700' : 'bg-gray-50'} p-3 rounded-lg`}
              >
                <Text
                  type='secondary'
                  className='text-xs font-medium mb-1 block'
                >
                  {t('updatedAt')}
                </Text>
                <Space size='small'>
                  <CalendarOutlined
                    className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}
                  />
                  <Text className='text-sm'>
                    {dayjs(order.updatedAt).format('MMM DD, YYYY HH:mm')}
                  </Text>
                </Space>
              </div>
            </div>

            {/* Notes Section - Compact if exists */}
            {order.notes && (
              <div>
                <div className='flex items-center space-x-2 mb-2'>
                  <FileTextOutlined
                    className={`text-base ${isDark ? 'text-gray-300' : 'text-gray-600'}`}
                  />
                  <Title
                    level={5}
                    className={`!mb-0 ${isDark ? 'text-white' : 'text-gray-900'}`}
                  >
                    {t('orderNotes')}
                  </Title>
                </div>
                <div
                  className={`${isDark ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'} border p-3 rounded-lg`}
                >
                  <Paragraph
                    className={`!mb-0 text-sm leading-relaxed ${isDark ? 'text-gray-300' : 'text-gray-700'}`}
                  >
                    {order.notes}
                  </Paragraph>
                </div>
              </div>
            )}
          </Card>
        </div>
      </div>

      {/* Verify Modal */}
      <Modal
        title={
          <div className='flex items-center space-x-3'>
            <div className='bg-green-100 p-2 rounded-lg'>
              <CheckCircleOutlined className='text-green-600 text-xl' />
            </div>
            <div>
              <Title level={4} className='!mb-0'>
                {t('approveModalTitle')}
              </Title>
              <Text type='secondary' className='text-sm'>
                {t('approveModalDescription')}
              </Text>
            </div>
          </div>
        }
        open={approveModalOpen}
        onCancel={() => {
          setApproveModalOpen(false);
          approveForm.resetFields();
        }}
        footer={null}
        width={600}
        className='modern-modal'
      >
        <div className='pt-6'>
          <Alert
            message={t('confirmOrderApproval')}
            description={t('confirmOrderApprovalDescription')}
            type='success'
            showIcon
            className='mb-6 rounded-lg'
          />

          <Form
            form={approveForm}
            layout='vertical'
            onFinish={handleApproveOrder}
            className='space-y-4'
          >
            <Form.Item
              label={
                <span className='font-medium'>
                  <SafetyOutlined className='mr-2 text-blue-500' />
                  {t('adminPassword')}
                </span>
              }
              name='password'
              rules={[
                { required: true, message: t('adminPasswordRequired') },
                { min: 6, message: t('adminPasswordMinLength') },
              ]}
            >
              <Password
                placeholder={t('adminPasswordPlaceholder')}
                size='large'
                iconRender={(visible) =>
                  visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                }
                className='rounded-lg'
              />
            </Form.Item>

            <Form.Item
              label={
                <span className='font-medium'>
                  <FileTextOutlined className='mr-2 text-blue-500' />
                  {t('additionalNotes')}
                </span>
              }
              name='notes'
            >
              <TextArea
                rows={4}
                placeholder={t('additionalNotesPlaceholder')}
                className='rounded-lg'
              />
            </Form.Item>

            <div className='flex justify-end space-x-3 pt-4 border-t'>
              <Button
                size='large'
                onClick={() => {
                  setApproveModalOpen(false);
                  approveForm.resetFields();
                }}
                className='px-6'
              >
                {t('cancel')}
              </Button>
              <Button
                type='primary'
                size='large'
                loading={adminReviewMutation.isPending}
                htmlType='submit'
                className='px-8 bg-green-600 hover:bg-green-700 border-green-600 hover:border-green-700 shadow-lg'
              >
                <CheckCircleOutlined className='mr-2' />
                {t('approve')}
              </Button>
            </div>
          </Form>
        </div>
      </Modal>

      {/* Reject Modal */}
      <Modal
        title={
          <div className='flex items-center space-x-3'>
            <div className='bg-red-100 p-2 rounded-lg'>
              <CloseCircleOutlined className='text-red-600 text-xl' />
            </div>
            <div>
              <Title level={4} className='!mb-0'>
                {t('cancelModalTitle')}
              </Title>
              <Text type='secondary' className='text-sm'>
                {t('cancelModalDescription')}
              </Text>
            </div>
          </div>
        }
        open={cancelModalOpen}
        onCancel={() => {
          setCancelModalOpen(false);
          cancelForm.resetFields();
        }}
        footer={null}
        width={600}
        className='modern-modal'
      >
        <div className='pt-6'>
          <Alert
            message={t('confirmOrderRejection')}
            description={t('confirmOrderRejectionDescription')}
            type='error'
            showIcon
            className='mb-6 rounded-lg'
          />

          <Form
            form={cancelForm}
            layout='vertical'
            onFinish={handleCancelOrder}
            className='space-y-4'
          >
            <Form.Item
              label={
                <span className='font-medium'>
                  <SafetyOutlined className='mr-2 text-blue-500' />
                  {t('adminPassword')}
                </span>
              }
              name='password'
              rules={[
                { required: true, message: t('adminPasswordRequired') },
                { min: 6, message: t('adminPasswordMinLength') },
              ]}
            >
              <Password
                placeholder={t('adminPasswordPlaceholder')}
                size='large'
                iconRender={(visible) =>
                  visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                }
                className='rounded-lg'
              />
            </Form.Item>

            <Form.Item
              label={
                <span className='font-medium'>
                  <ExclamationCircleOutlined className='mr-2 text-red-500' />
                  {t('reasonForRejection')}
                </span>
              }
              name='reason'
              rules={[
                {
                  required: true,
                  message: t('reasonRequired'),
                },
                { min: 10, message: t('reasonMinLength') },
              ]}
            >
              <TextArea
                rows={5}
                placeholder={t('reasonForRejectionPlaceholder')}
                className='rounded-lg'
                showCount
                maxLength={500}
              />
            </Form.Item>

            <div className='flex justify-end space-x-3 pt-4 border-t'>
              <Button
                size='large'
                onClick={() => {
                  setCancelModalOpen(false);
                  cancelForm.resetFields();
                }}
                className='px-6'
              >
                {t('cancel')}
              </Button>
              <Button
                type='primary'
                danger
                size='large'
                loading={adminReviewMutation.isPending}
                htmlType='submit'
                className='px-8 shadow-lg'
              >
                <CloseCircleOutlined className='mr-2' />
                {t('reject')}
              </Button>
            </div>
          </Form>
        </div>
      </Modal>
    </div>
  );
};

export default OrderReview;
